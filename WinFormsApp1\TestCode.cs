using System;
using System.Collections.Generic;
using System.Linq;

namespace WinFormsApp1
{
    // Neovim 편집 테스트용 클래스
    
    /// <summary>
    /// Neovim 테스트를 위한 간단한 클래스
    /// </summary>
    public class TestCode
    {
        // 간단한 속성들
        public string Name { get; set; }
        public int Age { get; set; }
        public List<string> Hobbies { get; set; }

        // 생성자
        public TestCode()
        {
            Name = "테스트";
            Age = 0;
            Hobbies = new List<string>();
        }

        public TestCode(string name, int age)
        {
            Name = name;
            Age = age;
            Hobbies = new List<string>();
        }

        // 간단한 메서드들 - Neovim 편집 테스트용
        public void AddHobby(string hobby)
        {
            if (!string.IsNullOrEmpty(hobby))
            {
                Hobbies.Add(hobby);
            }
        }

        public void RemoveHobby(string hobby)
        {
            Hobbies.Remove(hobby);
        }

        public string GetInfo()
        {
            var hobbiesStr = string.Join(", ", Hobbies);
            return $"이름: {Name}, 나이: {Age}, 취미: {hobbiesStr}";
        }

        // 계산 메서드들
        public int Add(int a, int b)
        {
            return a + b;
        }

        public int Multiply(int a, int b)
        {
            return a * b;
        }

        public bool IsEven(int number)
        {
            return number % 2 == 0;
        }

        // 배열/리스트 처리
        public int[] GetEvenNumbers(int[] numbers)
        {
            return numbers.Where(n => IsEven(n)).ToArray();
        }

        public string ReverseString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            return new string(input.Reverse().ToArray());
        }

        // 테스트용 메인 메서드
        public static void RunTests()
        {
            Console.WriteLine("=== Neovim 편집 테스트 시작 ===");

            var test = new TestCode("홍길동", 25);
            test.AddHobby("독서");
            test.AddHobby("영화감상");
            test.AddHobby("코딩");

            Console.WriteLine(test.GetInfo());
            Console.WriteLine($"5 + 3 = {test.Add(5, 3)}");
            Console.WriteLine($"4 * 7 = {test.Multiply(4, 7)}");
            Console.WriteLine($"10은 짝수? {test.IsEven(10)}");

            int[] numbers = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 };
            var evenNumbers = test.GetEvenNumbers(numbers);
            Console.WriteLine($"짝수들: [{string.Join(", ", evenNumbers)}]");

            string original = "Hello Neovim!";
            string reversed = test.ReverseString(original);
            Console.WriteLine($"원본: {original}");
            Console.WriteLine($"뒤집기: {reversed}");

            Console.WriteLine("=== 테스트 완료 ===");
        }
    }
}
